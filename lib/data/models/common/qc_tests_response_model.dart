import 'package:json_annotation/json_annotation.dart';
import '../completed_test/completed_test_model.dart';

part 'qc_tests_response_model.g.dart';

@JsonSerializable()
class QCTestsResponseModel {
  final bool success;
  final String? message;
  final QCTestsDataModel data;

  const QCTestsResponseModel({
    required this.success,
    this.message,
    required this.data,
  });

  factory QCTestsResponseModel.fromJson(Map<String, dynamic> json) =>
      _$QCTestsResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$QCTestsResponseModelToJson(this);
}

@JsonSerializable()
class QCTestsDataModel {
  @JsonKey(name: 'current_page')
  final int currentPage;
  final List<CompletedTestModel> data;

  const QCTestsDataModel({
    required this.currentPage,
    required this.data,
  });

  factory QCTestsDataModel.fromJson(Map<String, dynamic> json) =>
      _$QCTestsDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$QCTestsDataModelToJson(this);
}

@JsonSerializable()
class QCTestResponseModel {
  final bool success;
  final String? message;
  final QCTestDataModel data;

  const QCTestResponseModel({
    required this.success,
    this.message,
    required this.data,
  });

  factory QCTestResponseModel.fromJson(Map<String, dynamic> json) =>
      _$QCTestResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$QCTestResponseModelToJson(this);
}

@JsonSerializable()
class QCTestDataModel {
  final CompletedTestModel test;

  const QCTestDataModel({
    required this.test,
  });

  factory QCTestDataModel.fromJson(Map<String, dynamic> json) =>
      _$QCTestDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$QCTestDataModelToJson(this);
}
