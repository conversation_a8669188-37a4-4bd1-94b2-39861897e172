import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/router/app_routes.dart';
import '../bloc/job_allocation_bloc.dart';
import '../bloc/job_allocation_event.dart';
import '../bloc/job_allocation_state.dart';
import '../../../../domain/entities/job_allocation/job_allocation_entity.dart';

class JobDetailsScreen extends StatefulWidget {
  final int jobId;

  const JobDetailsScreen({
    super.key,
    required this.jobId,
  });

  @override
  State<JobDetailsScreen> createState() => _JobDetailsScreenState();
}

class _JobDetailsScreenState extends State<JobDetailsScreen> {
  @override
  void initState() {
    super.initState();
    // Load job allocation data using BLoC
    context.read<JobAllocationBloc>().add(
      JobAllocationLoadRequested(id: widget.jobId),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Job Details'),
      ),
      body: BlocBuilder<JobAllocationBloc, JobAllocationState>(
        builder: (context, state) {
          if (state is JobAllocationOperationLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else if (state is JobAllocationLoaded) {
            return _buildJobDetailsContent(state.jobAllocation);
          } else if (state is JobAllocationError) {
            return _buildErrorState(state);
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _buildJobDetailsContent(JobAllocationEntity jobAllocation) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status and Progress Card
          _buildStatusCard(jobAllocation),
          const SizedBox(height: 16),

          // Job Information
          _buildJobInfoCard(jobAllocation),
          const SizedBox(height: 16),

          // Sample Information
          _buildSampleInfoCard(jobAllocation),
          const SizedBox(height: 16),

          // Additional Information
          _buildAdditionalInfoCard(jobAllocation),
        ],
      ),
    );
  }

  Widget _buildErrorState(JobAllocationError state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading job details',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            state.failure.message,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              context.read<JobAllocationBloc>().add(
                JobAllocationLoadRequested(id: widget.jobId),
              );
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard(JobAllocationEntity jobAllocation) {
    final status = jobAllocation.status ?? 'pending';
    final priority = jobAllocation.priority ?? 'medium';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: _getStatusColor(status),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Status: ${_getStatusLabel(status)}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: _getStatusColor(status),
                      ),
                    ),
                  ],
                ),
                _buildPriorityChip(priority),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  'Job ID: JOB-${jobAllocation.id.toString().padLeft(6, '0')}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobInfoCard(JobAllocationEntity jobAllocation) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.work, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Job Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Test Request', 'TR-${jobAllocation.testRequestId.toString().padLeft(6, '0')}'),
            _buildInfoRow('Serial No', jobAllocation.serialNo),
            _buildInfoRow('Code Number', jobAllocation.codeNumber),
            _buildInfoRow('Due Date', jobAllocation.dueDate.toString().split(' ')[0]),
            _buildInfoRow('Creation Date', jobAllocation.creationDate.toString().split(' ')[0]),
            if (jobAllocation.createdAt != null)
              _buildInfoRow('Created At', jobAllocation.createdAt!.toString().split(' ')[0]),
          ],
        ),
      ),
    );
  }

  Widget _buildSampleInfoCard(JobAllocationEntity jobAllocation) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.science, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Sample Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Nature', jobAllocation.nature),
            _buildInfoRow('Quantity', jobAllocation.quantity),
            _buildInfoRow('Collection Date', jobAllocation.collectionDate.toString().split(' ')[0]),
            _buildInfoRow('Submission Date', jobAllocation.submissionDate.toString().split(' ')[0]),
            _buildInfoRow('Designation', jobAllocation.designation),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard(JobAllocationEntity jobAllocation) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Additional Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('User ID', jobAllocation.userId.toString()),
            _buildInfoRow('NABL Status', jobAllocation.nablStatus),
            if (jobAllocation.reportType != null)
              _buildInfoRow('Report Type', jobAllocation.reportType!),
            if (jobAllocation.remarks != null && jobAllocation.remarks!.isNotEmpty)
              _buildInfoRow('Remarks', jobAllocation.remarks!),
            if (jobAllocation.updatedAt != null)
              _buildInfoRow('Last Updated', jobAllocation.updatedAt!.toString().split(' ')[0]),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    IconData icon;
    
    switch (priority) {
      case 'high':
        color = Colors.red;
        icon = Icons.keyboard_arrow_up;
        break;
      case 'medium':
        color = Colors.orange;
        icon = Icons.remove;
        break;
      case 'low':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        break;
      default:
        color = Colors.grey;
        icon = Icons.remove;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            priority.toUpperCase(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'assigned':
        return Colors.blue;
      case 'in_progress':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusLabel(String status) {
    switch (status) {
      case 'assigned':
        return 'Assigned';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  void _editJob() {
    // TODO: Navigate to edit job screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit job functionality coming soon')),
    );
  }

  void _reassignJob() {
    // TODO: Show reassignment dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Reassign job functionality coming soon')),
    );
  }

  void _deleteJob() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Job'),
        content: const Text('Are you sure you want to delete this job? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Job deleted')),
              );
              context.pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showHistory() {
    // TODO: Show job history
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Job history functionality coming soon')),
    );
  }
}
